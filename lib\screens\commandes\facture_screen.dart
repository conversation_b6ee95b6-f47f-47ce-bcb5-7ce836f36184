import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/commande.dart';
import '../../models/client.dart';
import '../../providers/firebase_client_provider.dart';

class FactureScreen extends StatelessWidget {
  final Commande commande;

  const FactureScreen({super.key, required this.commande});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const SizedBox(width: 4),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Facture #${commande.id}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1F2937),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    'Détails de la facture',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Colors.grey.shade600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1F2937),
        toolbarHeight: 70,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _partagerFacture(context),
            tooltip: 'Partager',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () => _imprimerFacture(context),
            tooltip: 'Imprimer',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Consumer<FirebaseClientProvider>(
        builder: (context, clientProvider, child) {
          final client = clientProvider.obtenirClientParId(commande.clientId);

          if (client == null) {
            return const Center(child: Text('Client introuvable'));
          }

          final screenWidth = MediaQuery.of(context).size.width;
          final isSmallScreen = screenWidth < 480;
          final padding = isSmallScreen ? 16.0 : 20.0;

          return SingleChildScrollView(
            padding: EdgeInsets.all(padding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEnteteFacture(context),
                const SizedBox(height: 24),
                _buildInfosClient(context, client),
                const SizedBox(height: 24),
                _buildInfosCommande(context),
                const SizedBox(height: 24),
                _buildTableauItems(context),
                const SizedBox(height: 24),
                _buildTotaux(context),
                const SizedBox(height: 32),
                _buildPiedFacture(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEnteteFacture(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'FACTURE',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'N° ${commande.id}',
                        style: Theme.of(context).textTheme.titleMedium,
                        maxLines: 3,
                        softWrap: true,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Date: ${DateFormat('dd/MM/yyyy').format(commande.dateCommande)}',
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.end,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Statut: ${commande.statutFormate}',
                        style: TextStyle(
                          color: _getStatutColor(commande.statut),
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            Text(
              'Vitabrosse',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Text('km 5.5 Route Manzel Chaker'),
            const Text('Sfax 3013, Tunisia'),
            const Text('+216 74 652 692'),
            const Text('Email: <EMAIL>'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfosClient(BuildContext context, Client client) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'FACTURER À',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        client.nomComplet,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        client.email,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        client.primaryPhone,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        client.adresse,
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 2,
                        softWrap: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfosCommande(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'DÉTAILS DE LA COMMANDE',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Commande N°: ${commande.id}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                  maxLines: 2,
                  softWrap: true,
                ),
                const SizedBox(height: 4),
                Text(
                  'Date: ${DateFormat('dd/MM/yyyy à HH:mm').format(commande.dateCommande)}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            if (commande.notes != null) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${commande.notes}',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTableauItems(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ARTICLES',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: WidgetStateProperty.all(Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.1)),
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(8),
                ),
                columns: const [
                  DataColumn(
                    label: Text(
                      'Article',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Qté',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Prix unit.',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Remise',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Total',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    ),
                  ),
                ],
                rows: commande.items
                    .map(
                      (item) => DataRow(
                        cells: [
                          DataCell(Text(item.nomProduit)),
                          DataCell(
                            Text(
                              item.quantite.toString(),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          DataCell(
                            Text(
                              item.prixUnitaireFormate,
                              textAlign: TextAlign.right,
                            ),
                          ),
                          DataCell(
                            Text(
                              item.remise > 0
                                  ? '${item.montantRemise.toStringAsFixed(3)} DT'
                                  : '-',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: item.remise > 0
                                    ? Colors.green[600]
                                    : Colors.grey[600],
                                fontWeight: item.remise > 0
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              item.sousTotalFormate,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotaux(BuildContext context) {
    // Calculate subtotal before any discounts
    final sousTotal = commande.items.fold<double>(
        0.0, (sum, item) => sum + (item.prixUnitaire * item.quantite));

    // Calculate total discount amount from all items
    final totalRemise = commande.items.fold<double>(0.0, (sum, item) {
      if (item.remise > 0) {
        final itemSubtotal = item.prixUnitaire * item.quantite;
        return sum +
            (item.remiseEnPourcentage
                ? (itemSubtotal * item.remise / 100)
                : item.remise);
      }
      return sum;
    });

    // Calculate discount percentage for the whole order
    final remisePercentage =
        sousTotal > 0 ? (totalRemise / sousTotal * 100) : 0.0;

    final sousApresRemise = sousTotal - totalRemise;
    final tva = sousApresRemise * 0.20; // TVA 20%
    final totalTTC = sousApresRemise + tva;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: const Color(0xFF6366F1).withValues(alpha: 0.3)),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF6366F1).withValues(alpha: 0.05),
              const Color(0xFF8B5CF6).withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Sous-total HT:'),
                Text('${sousTotal.toStringAsFixed(3)} DT'),
              ],
            ),
            if (totalRemise > 0) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Remise totale (${remisePercentage.toStringAsFixed(1)}%):',
                    style: TextStyle(color: Colors.green[600]),
                  ),
                  Text(
                    '-${totalRemise.toStringAsFixed(3)} DT',
                    style: TextStyle(
                      color: Colors.green[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Sous-total après remise:'),
                  Text('${sousApresRemise.toStringAsFixed(3)} DT'),
                ],
              ),
            ],
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('TVA (20%):'),
                Text('${tva.toStringAsFixed(3)} DT'),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'TOTAL TTC:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${totalTTC.toStringAsFixed(3)} DT',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPiedFacture(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'CONDITIONS DE PAIEMENT',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• Paiement à 30 jours'),
            const Text('• Escompte 2% si paiement sous 8 jours'),
            const Text('• Pénalités de retard: 3 fois le taux légal'),
            const SizedBox(height: 16),
            Text(
              'Merci pour votre confiance !',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatutColor(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return const Color(0xFF3B82F6); // Blue for waiting (instead of orange)
      case StatutCommande.confirmee:
        return const Color(
            0xFF059669); // Green for confirmed (like devis accepted)
      case StatutCommande.enPreparation:
        return const Color(0xFF1D4ED8); // Darker blue for preparation
      case StatutCommande.expediee:
        return const Color(0xFF6366F1); // Indigo for shipped
      case StatutCommande.livree:
        return const Color(0xFF10B981); // Green for delivered
      case StatutCommande.annulee:
        return Colors.red[600]!; // Red for cancelled (like devis refused)
    }
  }

  void _partagerFacture(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de partage en cours de développement'),
        backgroundColor: Color(0xFF3B82F6),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _imprimerFacture(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité d\'impression en cours de développement'),
        backgroundColor: Color(0xFF3B82F6),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
