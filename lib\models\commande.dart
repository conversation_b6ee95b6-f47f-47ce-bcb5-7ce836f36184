import 'commande_item.dart';

// Énumération des statuts de commande
enum StatutCommande {
  enAttente,
  confirmee,
  enPreparation,
  expediee,
  livree,
  annulee,
}

class Commande {
  final String? id;
  final String clientId;
  final DateTime dateCommande;
  final DateTime? dateLivraison; // Date de livraison souhaitée
  final StatutCommande statut;
  final double montantTotal;
  final String? notes;
  final List<CommandeItem> items;
  final double remisePourcentage; // Remise globale en pourcentage
  final double remiseMontant; // Remise globale en montant fixe
  final double tauxTva; // Taux de TVA
  final String? priorite; // Priorité de la commande (normale, urgente, etc.)
  final String? contactCommercial; // Contact commercial responsable

  Commande({
    this.id,
    required this.clientId,
    required this.dateCommande,
    this.dateLivraison,
    this.statut = StatutCommande.enAttente,
    required this.montantTotal,
    this.notes,
    this.items = const [],
    this.remisePourcentage = 0.0,
    this.remiseMontant = 0.0,
    this.tauxTva = 19.0,
    this.priorite,
    this.contactCommercial,
  });

  // Convertir une Commande en Map pour la base de données
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'clientId': clientId,
      'dateCommande': dateCommande.toIso8601String(),
      'statut': _getStatutString(statut),
      'montantTotal': montantTotal,
      'remisePourcentage': remisePourcentage,
      'remiseMontant': remiseMontant,
      'tauxTva': tauxTva,
      'items': items.map((item) => item.toMap()).toList(),
    };

    // Only add optional fields if they're not null
    if (notes != null) {
      map['notes'] = notes;
    }
    if (dateLivraison != null) {
      map['dateLivraison'] = dateLivraison!.toIso8601String();
    }
    if (priorite != null) {
      map['priorite'] = priorite;
    }
    if (contactCommercial != null) {
      map['contactCommercial'] = contactCommercial;
    }

    return map;
  }

  // Convertir StatutCommande en string
  String _getStatutString(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'en_attente';
      case StatutCommande.confirmee:
        return 'confirmee';
      case StatutCommande.enPreparation:
        return 'en_preparation';
      case StatutCommande.expediee:
        return 'expediee';
      case StatutCommande.livree:
        return 'livree';
      case StatutCommande.annulee:
        return 'annulee';
    }
  }

  // Convertir string en StatutCommande
  static StatutCommande _getStatutFromString(dynamic statut) {
    // Handle both string and int for backward compatibility
    if (statut is int) {
      return StatutCommande.values[statut];
    }

    switch (statut.toString()) {
      case 'en_attente':
        return StatutCommande.enAttente;
      case 'confirmee':
        return StatutCommande.confirmee;
      case 'en_preparation':
        return StatutCommande.enPreparation;
      case 'expediee':
        return StatutCommande.expediee;
      case 'livree':
        return StatutCommande.livree;
      case 'annulee':
        return StatutCommande.annulee;
      default:
        return StatutCommande.enAttente;
    }
  }

  // Créer une Commande à partir d'une Map de la base de données
  factory Commande.fromMap(Map<String, dynamic> map) {
    List<CommandeItem> itemsList = [];

    // Parse items if they exist in the map
    if (map['items'] != null) {
      try {
        itemsList = (map['items'] as List)
            .map(
              (item) => CommandeItem.fromMap(item as Map<String, dynamic>),
            )
            .toList();
      } catch (e) {
        print('Erreur lors du parsing des items: $e');
        itemsList = [];
      }
    }

    return Commande(
      id: map['id'],
      clientId: map['clientId'],
      dateCommande: DateTime.parse(map['dateCommande']),
      dateLivraison: map['dateLivraison'] != null
          ? DateTime.parse(map['dateLivraison'])
          : null,
      statut: _getStatutFromString(map['statut']),
      montantTotal: map['montantTotal'].toDouble(),
      notes: map['notes'],
      items: itemsList,
      remisePourcentage: (map['remisePourcentage'] ?? 0.0).toDouble(),
      remiseMontant: (map['remiseMontant'] ?? 0.0).toDouble(),
      tauxTva: (map['tauxTva'] ?? 19.0).toDouble(),
      priorite: map['priorite'],
      contactCommercial: map['contactCommercial'],
    );
  }

  // Créer une copie de la commande avec des modifications
  Commande copyWith({
    String? id,
    String? clientId,
    DateTime? dateCommande,
    DateTime? dateLivraison,
    StatutCommande? statut,
    double? montantTotal,
    String? notes,
    List<CommandeItem>? items,
    double? remisePourcentage,
    double? remiseMontant,
    double? tauxTva,
    String? priorite,
    String? contactCommercial,
  }) {
    return Commande(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      dateCommande: dateCommande ?? this.dateCommande,
      dateLivraison: dateLivraison ?? this.dateLivraison,
      statut: statut ?? this.statut,
      montantTotal: montantTotal ?? this.montantTotal,
      notes: notes ?? this.notes,
      items: items ?? this.items,
      remisePourcentage: remisePourcentage ?? this.remisePourcentage,
      remiseMontant: remiseMontant ?? this.remiseMontant,
      tauxTva: tauxTva ?? this.tauxTva,
      priorite: priorite ?? this.priorite,
      contactCommercial: contactCommercial ?? this.contactCommercial,
    );
  }

  // Calculs financiers
  double get sousTotal {
    return items.fold(
        0.0, (total, item) => total + (item.prixUnitaire * item.quantite));
  }

  double get totalRemiseArticles {
    return items.fold(0.0, (total, item) => total + item.montantRemise);
  }

  double get montantRemiseGlobale {
    if (remiseMontant > 0) {
      return remiseMontant;
    }
    return sousTotal * (remisePourcentage / 100);
  }

  double get totalHT {
    return sousTotal - totalRemiseArticles - montantRemiseGlobale;
  }

  double get montantTva {
    return totalHT * (tauxTva / 100);
  }

  double get totalTTC {
    return totalHT + montantTva;
  }

  // Calculer le montant total à partir des items (méthode legacy)
  double calculerMontantTotal() {
    return totalTTC;
  }

  // Nombre total d'articles
  int get nombreArticles =>
      items.fold(0, (total, item) => total + item.quantite);

  // Statut formaté
  String get statutFormate {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  // Montant formaté
  String get montantFormate => '${montantTotal.toStringAsFixed(3)} DT';

  @override
  String toString() {
    return 'Commande{id: $id, clientId: $clientId, statut: $statut, montant: $montantTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Commande && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
